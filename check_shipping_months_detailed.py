import csv
from datetime import datetime

# Check what months the shipments should be assigned to
shipment_months = {}
total_carrier_fee = 0
total_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        # Get the shipped date
        shipped_date_str = row['Date - Shipped Date']
        carrier_fee = float(row['Carrier - Fee'])
        order_source = row['Order - Source']
        void_flag = row['Shipment - Void Flag']
        
        total_carrier_fee += carrier_fee
        total_shipments += 1
        
        if shipped_date_str and void_flag.lower() != 'true':
            try:
                # Parse the date
                shipped_date = datetime.strptime(shipped_date_str, '%m/%d/%Y %I:%M:%S %p')
                month = shipped_date.month - 1  # JavaScript months are 0-based
                year = shipped_date.year
                
                month_key = f"{year}-{month:02d}"
                if month_key not in shipment_months:
                    shipment_months[month_key] = {
                        'count': 0,
                        'total_carrier_fee': 0,
                        'month_name': shipped_date.strftime('%B %Y'),
                        'js_month': month,
                        'year': year,
                        'draft_orders': 0,
                        'draft_order_fees': 0,
                        'regular_orders': 0,
                        'regular_order_fees': 0
                    }
                
                shipment_months[month_key]['count'] += 1
                shipment_months[month_key]['total_carrier_fee'] += carrier_fee
                
                if order_source == 'shopify_draft_order':
                    shipment_months[month_key]['draft_orders'] += 1
                    shipment_months[month_key]['draft_order_fees'] += carrier_fee
                else:
                    shipment_months[month_key]['regular_orders'] += 1
                    shipment_months[month_key]['regular_order_fees'] += carrier_fee
                    
            except ValueError as e:
                print(f"Error parsing date '{shipped_date_str}': {e}")

print("=== SHIPMENT BREAKDOWN BY MONTH ===")
print(f"Total shipments in CSV: {total_shipments}")
print(f"Total carrier fees in CSV: ${total_carrier_fee:.2f}")
print()

# Sort by year-month
sorted_months = sorted(shipment_months.items())

for month_key, data in sorted_months:
    print(f"Month: {data['month_name']} (JS month: {data['js_month']}, Year: {data['year']})")
    print(f"  Total shipments: {data['count']}")
    print(f"  Total carrier fees: ${data['total_carrier_fee']:.2f}")
    print(f"  Regular orders: {data['regular_orders']} (${data['regular_order_fees']:.2f})")
    print(f"  Draft orders: {data['draft_orders']} (${data['draft_order_fees']:.2f})")
    
    # Calculate expected totals for this month
    regular_markup = data['regular_order_fees'] * 0.10
    regular_with_markup = data['regular_order_fees'] + regular_markup
    regular_fulfillment = data['regular_orders'] * 1.50
    regular_total = regular_with_markup + regular_fulfillment
    
    all_markup = data['total_carrier_fee'] * 0.10
    all_with_markup = data['total_carrier_fee'] + all_markup
    all_fulfillment = data['count'] * 1.50
    all_total = all_with_markup + all_fulfillment
    
    print(f"  Expected (regular only): ${regular_with_markup:.2f} + ${regular_fulfillment:.2f} = ${regular_total:.2f}")
    print(f"  Expected (all): ${all_with_markup:.2f} + ${all_fulfillment:.2f} = ${all_total:.2f}")
    print()

# Check current month (May 2025 would be month 4 in JavaScript)
current_month = 4  # May (0-based)
current_year = 2025

print("=== CURRENT MONTH ANALYSIS (May 2025, JS month 4) ===")
may_key = f"{current_year}-{current_month:02d}"
if may_key in shipment_months:
    may_data = shipment_months[may_key]
    print(f"May 2025 shipments: {may_data['count']}")
    print(f"May 2025 carrier fees: ${may_data['total_carrier_fee']:.2f}")
    print(f"May 2025 regular orders: {may_data['regular_orders']} (${may_data['regular_order_fees']:.2f})")
    
    # This should match what the page shows if filtering is working correctly
    expected_base = may_data['regular_order_fees']
    expected_markup = expected_base * 0.10
    expected_with_markup = expected_base + expected_markup
    
    print(f"Expected base shipping cost: ${expected_base:.2f}")
    print(f"Expected with 10% markup: ${expected_with_markup:.2f}")
    print(f"Expected item count: {may_data['regular_orders']}")
else:
    print("No May 2025 data found!")
