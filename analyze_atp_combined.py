import csv
from datetime import datetime

print("=== COMBINED AMERICAN TRIGGER PULLERS CSV ANALYSIS ===")

# Analyze May CSV
may_total_carrier_fee = 0
may_total_shipments = 0
may_voided_shipments = 0
may_draft_order_shipments = 0
may_valid_non_draft_carrier_fee = 0
may_valid_non_draft_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row['Carrier - Fee'])
        void_flag = row['Shipment - Void Flag']
        order_source = row['Order - Source']
        
        may_total_carrier_fee += carrier_fee
        may_total_shipments += 1
        
        # Check if shipment is voided
        if void_flag.lower() == 'true':
            may_voided_shipments += 1
        elif order_source == 'shopify_draft_order':
            may_draft_order_shipments += 1
        else:
            may_valid_non_draft_carrier_fee += carrier_fee
            may_valid_non_draft_shipments += 1

# Analyze June CSV - check column headers first
june_total_carrier_fee = 0
june_total_shipments = 0
june_valid_non_draft_carrier_fee = 0
june_valid_non_draft_shipments = 0

# Check if June CSV has same format as May or different format like PLC
with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-june.csv', 'r') as file:
    first_line = file.readline().strip()
    print(f"June CSV headers: {first_line}")
    
    # Reset file pointer
    file.seek(0)
    reader = csv.DictReader(file)
    
    # Check if it has the full format like May or simplified format like PLC June
    if 'Carrier - Fee' in first_line:
        # Full format like May
        for row in reader:
            carrier_fee = float(row['Carrier - Fee'])
            void_flag = row['Shipment - Void Flag']
            order_source = row['Order - Source']
            
            june_total_carrier_fee += carrier_fee
            june_total_shipments += 1
            
            # Only count valid non-draft orders
            if void_flag.lower() != 'true' and order_source != 'shopify_draft_order':
                june_valid_non_draft_carrier_fee += carrier_fee
                june_valid_non_draft_shipments += 1
    else:
        # Simplified format like PLC June
        for row in reader:
            carrier_fee = float(row['Carrier Fee'])
            
            june_total_carrier_fee += carrier_fee
            june_total_shipments += 1
            
            # Assume all June shipments are valid non-draft
            june_valid_non_draft_carrier_fee += carrier_fee
            june_valid_non_draft_shipments += 1

print("\n=== MAY CSV DATA ===")
print(f"Total shipments: {may_total_shipments}")
print(f"Total carrier fees: ${may_total_carrier_fee:.2f}")
print(f"Voided shipments: {may_voided_shipments}")
print(f"Draft order shipments: {may_draft_order_shipments}")
print(f"Valid non-draft shipments: {may_valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${may_valid_non_draft_carrier_fee:.2f}")

print("\n=== JUNE CSV DATA ===")
print(f"Total shipments: {june_total_shipments}")
print(f"Total carrier fees: ${june_total_carrier_fee:.2f}")
print(f"Valid non-draft shipments: {june_valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${june_valid_non_draft_carrier_fee:.2f}")

print("\n=== COMBINED CSV DATA ===")
combined_shipments = may_valid_non_draft_shipments + june_valid_non_draft_shipments
combined_carrier_fees = may_valid_non_draft_carrier_fee + june_valid_non_draft_carrier_fee

print(f"Combined valid non-draft shipments: {combined_shipments}")
print(f"Combined valid non-draft carrier fees: ${combined_carrier_fees:.2f}")

markup = combined_carrier_fees * 0.10
total_with_markup = combined_carrier_fees + markup
fulfillment_costs = combined_shipments * 1.50
expected_total = total_with_markup + fulfillment_costs

print(f"Expected 10% markup: ${markup:.2f}")
print(f"Expected total with markup: ${total_with_markup:.2f}")
print(f"Expected fulfillment costs ({combined_shipments} x $1.50): ${fulfillment_costs:.2f}")
print(f"Expected grand total: ${expected_total:.2f}")

print("\n=== COMPARISON WITH DATABASE RESULTS ===")
print("Database showed (from previous query):")
print("  May 2025: 1332 items, $8,357.17 base, $9,192.89 total")
print("  June 2025: 1086 items, $7,089.58 base, $7,798.54 total")
print("  Combined: 2418 items, $15,446.75 base, $16,991.43 total")

db_combined_items = 2418
db_combined_base = 15446.75
db_combined_total = 16991.43

print(f"\nCSV vs DB differences:")
print(f"  Quantity: {db_combined_items - combined_shipments} ({db_combined_items} DB vs {combined_shipments} CSV)")
print(f"  Base cost: ${db_combined_base - combined_carrier_fees:.2f} (${db_combined_base:.2f} DB vs ${combined_carrier_fees:.2f} CSV)")
print(f"  Total cost: ${db_combined_total - total_with_markup:.2f} (${db_combined_total:.2f} DB vs ${total_with_markup:.2f} CSV)")

if abs(db_combined_items - combined_shipments) < 50 and abs(db_combined_base - combined_carrier_fees) < 500:
    print("\n✅ GOOD MATCH! The ShipStation integration is working well.")
    print("Small differences are likely due to:")
    print("  - Additional shipments processed after CSV export")
    print("  - Minor timing differences in data collection")
else:
    print("\n❌ SIGNIFICANT DISCREPANCY DETECTED")
    print("Further investigation needed.")
    
    # Break down by month
    print(f"\n=== MONTH-BY-MONTH ANALYSIS ===")
    print(f"May - CSV: {may_valid_non_draft_shipments} items, ${may_valid_non_draft_carrier_fee:.2f}")
    print(f"May - DB:  1332 items, $8,357.17")
    may_diff_items = 1332 - may_valid_non_draft_shipments
    may_diff_cost = 8357.17 - may_valid_non_draft_carrier_fee
    print(f"May difference: {may_diff_items} items, ${may_diff_cost:.2f}")
    
    print(f"\nJune - CSV: {june_valid_non_draft_shipments} items, ${june_valid_non_draft_carrier_fee:.2f}")
    print(f"June - DB:  1086 items, $7,089.58")
    june_diff_items = 1086 - june_valid_non_draft_shipments
    june_diff_cost = 7089.58 - june_valid_non_draft_carrier_fee
    print(f"June difference: {june_diff_items} items, ${june_diff_cost:.2f}")
