import csv
from datetime import datetime

print("=== COMBINED PLTDADDY CSV ANALYSIS ===")

# Analyze May CSV
may_total_carrier_fee = 0
may_total_shipments = 0
may_voided_shipments = 0
may_draft_order_shipments = 0
may_valid_non_draft_carrier_fee = 0
may_valid_non_draft_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/pld-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        # May CSV has different format - carrier fee is in column 5 (6th column)
        carrier_fee = float(row['Carrier - Fee'])
        void_flag = row['Shipment - Void Flag']
        order_source = row['Order - Source']
        
        may_total_carrier_fee += carrier_fee
        may_total_shipments += 1
        
        # Check if shipment is voided
        if void_flag.lower() == 'true':
            may_voided_shipments += 1
        elif order_source == 'shopify_draft_order':
            may_draft_order_shipments += 1
        else:
            may_valid_non_draft_carrier_fee += carrier_fee
            may_valid_non_draft_shipments += 1

print("=== MAY CSV DATA ===")
print(f"Total shipments: {may_total_shipments}")
print(f"Total carrier fees: ${may_total_carrier_fee:.2f}")
print(f"Voided shipments: {may_voided_shipments}")
print(f"Draft order shipments: {may_draft_order_shipments}")
print(f"Valid non-draft shipments: {may_valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${may_valid_non_draft_carrier_fee:.2f}")

# Analyze June CSV
june_total_carrier_fee = 0
june_total_shipments = 0
june_valid_non_draft_carrier_fee = 0
june_valid_non_draft_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/pld-shipments-june.csv', 'r') as file:
    reader = csv.DictReader(file)
    
    # Check if it has the full format like May or simplified format like PLC June
    first_row = next(reader)
    file.seek(0)
    reader = csv.DictReader(file)
    first_line = ','.join(first_row.keys())
    
    if 'Carrier - Fee' in first_line:
        # Full format like May
        for row in reader:
            carrier_fee = float(row['Carrier - Fee'])
            void_flag = row['Shipment - Void Flag']
            order_source = row['Order - Source']
            
            june_total_carrier_fee += carrier_fee
            june_total_shipments += 1
            
            # Only count valid non-draft orders
            if void_flag.lower() != 'true' and order_source != 'shopify_draft_order':
                june_valid_non_draft_carrier_fee += carrier_fee
                june_valid_non_draft_shipments += 1
    else:
        # Simplified format like PLC June - carrier fee is in column 1 (2nd column)
        for row in reader:
            carrier_fee = float(row['Carrier Fee'])
            
            june_total_carrier_fee += carrier_fee
            june_total_shipments += 1
            
            # Assume all June shipments are valid non-draft
            june_valid_non_draft_carrier_fee += carrier_fee
            june_valid_non_draft_shipments += 1

print("\n=== JUNE CSV DATA ===")
print(f"Total shipments: {june_total_shipments}")
print(f"Total carrier fees: ${june_total_carrier_fee:.2f}")
print(f"Valid non-draft shipments: {june_valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${june_valid_non_draft_carrier_fee:.2f}")

print("\n=== COMBINED CSV DATA ===")
combined_shipments = may_valid_non_draft_shipments + june_valid_non_draft_shipments
combined_carrier_fees = may_valid_non_draft_carrier_fee + june_valid_non_draft_carrier_fee

print(f"Combined valid non-draft shipments: {combined_shipments}")
print(f"Combined valid non-draft carrier fees: ${combined_carrier_fees:.2f}")

markup = combined_carrier_fees * 0.10
total_with_markup = combined_carrier_fees + markup
fulfillment_costs = combined_shipments * 1.50
expected_total = total_with_markup + fulfillment_costs

print(f"Expected 10% markup: ${markup:.2f}")
print(f"Expected total with markup: ${total_with_markup:.2f}")
print(f"Expected fulfillment costs ({combined_shipments} x $1.50): ${fulfillment_costs:.2f}")
print(f"Expected grand total: ${expected_total:.2f}")

print("\n=== COMPARISON WITH DATABASE RESULTS ===")
print("Database showed:")
print("  May 2025: 3155 items, $18,170.02 base, $19,987.02 total")
print("  June 2025: 2810 items, $16,171.16 base, $17,788.28 total")
print("  April 2025: 17 items, $93.73 base, $103.10 total")
print("  Combined (May+June): 5965 items, $34,341.18 base, $37,775.30 total")

db_combined_items = 5965  # 3155 + 2810
db_combined_base = 34341.18  # 18170.02 + 16171.16
db_combined_total = 37775.30  # 19987.02 + 17788.28

print(f"\nCSV vs DB differences:")
print(f"  Quantity: {db_combined_items - combined_shipments} ({db_combined_items} DB vs {combined_shipments} CSV)")
print(f"  Base cost: ${db_combined_base - combined_carrier_fees:.2f} (${db_combined_base:.2f} DB vs ${combined_carrier_fees:.2f} CSV)")
print(f"  Total cost: ${db_combined_total - expected_total:.2f} (${db_combined_total:.2f} DB vs ${expected_total:.2f} CSV)")

if abs(db_combined_items - combined_shipments) < 50 and abs(db_combined_base - combined_carrier_fees) < 500:
    print("\n✅ GOOD MATCH! The ShipStation integration is working well.")
    print("Small differences are likely due to:")
    print("  - Additional shipments processed after CSV export")
    print("  - Minor timing differences in data collection")
else:
    print("\n❌ SIGNIFICANT DISCREPANCY DETECTED")
    print("Further investigation needed.")

print("\n=== MONTH-BY-MONTH ANALYSIS ===")
print(f"May - CSV: {may_valid_non_draft_shipments} items, ${may_valid_non_draft_carrier_fee:.2f}")
print(f"May - DB:  3155 items, $18,170.02")
may_quantity_diff = 3155 - may_valid_non_draft_shipments
may_base_diff = 18170.02 - may_valid_non_draft_carrier_fee
print(f"May difference: {may_quantity_diff:+d} items, ${may_base_diff:+.2f}")

print(f"\nJune - CSV: {june_valid_non_draft_shipments} items, ${june_valid_non_draft_carrier_fee:.2f}")
print(f"June - DB:  2810 items, $16,171.16")
june_quantity_diff = 2810 - june_valid_non_draft_shipments
june_base_diff = 16171.16 - june_valid_non_draft_carrier_fee
print(f"June difference: {june_quantity_diff:+d} items, ${june_base_diff:+.2f}")
