import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function queryShippingCosts() {
  try {
    console.log('=== QUERYING SHIPPING COSTS FOR MAY 2025 ===');
    
    // Query for May 2025 (month 4, year 2025)
    const mayShippingCosts = await prisma.shippingCost.findMany({
      where: {
        shop: 'american-trigger-pullers.myshopify.com',
        month: 4,
        year: 2025
      },
      orderBy: {
        storeId: 'asc'
      }
    });

    console.log(`Found ${mayShippingCosts.length} shipping cost records for May 2025`);
    
    let totalQuantity = 0;
    let totalShippingCost = 0;
    let totalMarkupAmount = 0;
    let totalAmount = 0;

    mayShippingCosts.forEach((record, index) => {
      console.log(`\nRecord ${index + 1}:`);
      console.log(`  Store ID: ${record.storeId}`);
      console.log(`  Quantity: ${record.quantity}`);
      console.log(`  Shipping Cost: $${Number(record.shippingCost).toFixed(2)}`);
      console.log(`  Markup Amount: $${Number(record.markupAmount).toFixed(2)}`);
      console.log(`  Total Amount: $${Number(record.totalAmount).toFixed(2)}`);
      console.log(`  Created: ${record.createdAt}`);
      console.log(`  Updated: ${record.updatedAt}`);
      
      totalQuantity += record.quantity;
      totalShippingCost += Number(record.shippingCost);
      totalMarkupAmount += Number(record.markupAmount);
      totalAmount += Number(record.totalAmount);
    });

    console.log('\n=== TOTALS ===');
    console.log(`Total Quantity: ${totalQuantity}`);
    console.log(`Total Shipping Cost: $${totalShippingCost.toFixed(2)}`);
    console.log(`Total Markup Amount: $${totalMarkupAmount.toFixed(2)}`);
    console.log(`Total Amount: $${totalAmount.toFixed(2)}`);
    
    // Also check if there are any records for other months
    console.log('\n=== ALL SHIPPING COST RECORDS FOR ATP ===');
    const allRecords = await prisma.shippingCost.findMany({
      where: {
        shop: 'american-trigger-pullers.myshopify.com'
      },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
        { storeId: 'asc' }
      ]
    });

    console.log(`Found ${allRecords.length} total shipping cost records`);
    
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    allRecords.forEach((record, index) => {
      const monthName = monthNames[record.month];
      console.log(`${index + 1}. ${monthName} ${record.year}, Store ${record.storeId}: ${record.quantity} items, $${Number(record.totalAmount).toFixed(2)}`);
    });

  } catch (error) {
    console.error('Error querying shipping costs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

queryShippingCosts();
