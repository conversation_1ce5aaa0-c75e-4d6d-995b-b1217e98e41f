import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function checkPltDaddyData() {
  try {
    console.log('=== CHECKING PLTDADDY DATA IN DATABASE ===');

    // First, let's see what shops exist in the database
    const allShops = await prisma.shippingCost.findMany({
      select: {
        shop: true
      },
      distinct: ['shop']
    });

    console.log('Available shops in database:', allShops.map(s => s.shop));

    // Check all shipping costs for pltdaddy (try different variations)
    const possibleShopNames = [
      'pltdaddy.myshopify.com',
      'pltdaddy',
      'platoon-daddy.myshopify.com',
      'platoon-daddy'
    ];

    let allShippingCosts = [];
    let actualShopName = null;

    for (const shopName of possibleShopNames) {
      const costs = await prisma.shippingCost.findMany({
        where: {
          shop: shopName
        },
        orderBy: [
          { year: 'desc' },
          { month: 'desc' },
          { storeId: 'asc' }
        ]
      });

      if (costs.length > 0) {
        allShippingCosts = costs;
        actualShopName = shopName;
        console.log(`Found data for shop: ${shopName}`);
        break;
      }
    }

    console.log(`Found ${allShippingCosts.length} shipping cost records for PltDaddy`);

    if (allShippingCosts.length === 0) {
      console.log('❌ No shipping cost records found for PltDaddy');
      return;
    }

    // Group by month/year
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const groupedData = {};
    let totalDbAmount = 0;

    allShippingCosts.forEach(record => {
      const monthKey = `${record.year}-${record.month}`;
      const monthName = `${monthNames[record.month]} ${record.year}`;

      if (!groupedData[monthKey]) {
        groupedData[monthKey] = {
          monthName,
          records: [],
          totalQuantity: 0,
          totalShippingCost: 0,
          totalMarkupAmount: 0,
          totalAmount: 0
        };
      }

      groupedData[monthKey].records.push(record);
      groupedData[monthKey].totalQuantity += record.quantity;
      groupedData[monthKey].totalShippingCost += Number(record.shippingCost);
      groupedData[monthKey].totalMarkupAmount += Number(record.markupAmount);
      groupedData[monthKey].totalAmount += Number(record.totalAmount);

      totalDbAmount += Number(record.totalAmount);
    });

    console.log('\n=== DATABASE RECORDS BY MONTH ===');
    Object.keys(groupedData).sort().forEach(monthKey => {
      const data = groupedData[monthKey];
      console.log(`\n${data.monthName}:`);
      console.log(`  Records: ${data.records.length}`);
      console.log(`  Total Quantity: ${data.totalQuantity}`);
      console.log(`  Total Shipping Cost: $${data.totalShippingCost.toFixed(2)}`);
      console.log(`  Total Markup Amount: $${data.totalMarkupAmount.toFixed(2)}`);
      console.log(`  Total Amount: $${data.totalAmount.toFixed(2)}`);

      // Show individual store records
      data.records.forEach(record => {
        console.log(`    Store ${record.storeId}: ${record.quantity} items, $${Number(record.totalAmount).toFixed(2)}`);
      });
    });

    console.log(`\n=== TOTAL DATABASE AMOUNT: $${totalDbAmount.toFixed(2)} ===`);

    // Now analyze the CSV files
    console.log('\n=== ANALYZING CSV FILES ===');

    // Read May CSV
    const mayData = await analyzeCsvFile('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/pld-shipments-5-25.csv');
    console.log('\nMAY CSV DATA:');
    console.log(`  Total Records: ${mayData.totalRecords}`);
    console.log(`  Total Carrier Fees: $${mayData.totalCarrierFees.toFixed(2)}`);
    console.log(`  Date Range: ${mayData.dateRange.min} to ${mayData.dateRange.max}`);

    // Read June CSV
    const juneData = await analyzeCsvFile('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/pld-shipments-june.csv');
    console.log('\nJUNE CSV DATA:');
    console.log(`  Total Records: ${juneData.totalRecords}`);
    console.log(`  Total Carrier Fees: $${juneData.totalCarrierFees.toFixed(2)}`);
    console.log(`  Date Range: ${juneData.dateRange.min} to ${juneData.dateRange.max}`);

    const totalCsvAmount = mayData.totalCarrierFees + juneData.totalCarrierFees;
    console.log(`\n=== TOTAL CSV AMOUNT: $${totalCsvAmount.toFixed(2)} ===`);

    // Compare amounts
    console.log('\n=== COMPARISON ===');
    console.log(`Database Total: $${totalDbAmount.toFixed(2)}`);
    console.log(`CSV Total: $${totalCsvAmount.toFixed(2)}`);
    console.log(`Difference: $${(totalDbAmount - totalCsvAmount).toFixed(2)}`);

    const percentageMatch = totalCsvAmount > 0 ? (totalDbAmount / totalCsvAmount * 100) : 0;
    console.log(`Database captures ${percentageMatch.toFixed(1)}% of CSV data`);

    if (Math.abs(totalDbAmount - totalCsvAmount) < 1) {
      console.log('✅ Amounts match closely!');
    } else if (totalDbAmount < totalCsvAmount) {
      console.log('⚠️  Database has less data than CSV files');
    } else {
      console.log('⚠️  Database has more data than CSV files');
    }

  } catch (error) {
    console.error('Error checking PltDaddy data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function analyzeCsvFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n').filter(line => line.trim());

  // Skip header
  const dataLines = lines.slice(1);

  let totalCarrierFees = 0;
  let minDate = null;
  let maxDate = null;

  dataLines.forEach(line => {
    const columns = line.split(',');

    // For the May CSV format (different structure)
    if (filePath.includes('5-25.csv')) {
      // Carrier Fee is in column index 5 (6th column)
      const carrierFeeStr = columns[5]?.replace(/"/g, '');
      const carrierFee = parseFloat(carrierFeeStr) || 0;
      totalCarrierFees += carrierFee;

      // Date is in column 26 (Date - Shipped Date)
      const dateStr = columns[26]?.replace(/"/g, '');
      if (dateStr && dateStr !== '') {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          if (!minDate || date < minDate) minDate = date;
          if (!maxDate || date > maxDate) maxDate = date;
        }
      }
    } else {
      // For the June CSV format
      // Carrier Fee is in column index 1 (2nd column)
      const carrierFeeStr = columns[1]?.replace(/"/g, '');
      const carrierFee = parseFloat(carrierFeeStr) || 0;
      totalCarrierFees += carrierFee;

      // Ship Date is in column index 2 (3rd column)
      const dateStr = columns[2]?.replace(/"/g, '');
      if (dateStr && dateStr !== '') {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          if (!minDate || date < minDate) minDate = date;
          if (!maxDate || date > maxDate) maxDate = date;
        }
      }
    }
  });

  return {
    totalRecords: dataLines.length,
    totalCarrierFees,
    dateRange: {
      min: minDate ? minDate.toISOString().split('T')[0] : 'N/A',
      max: maxDate ? maxDate.toISOString().split('T')[0] : 'N/A'
    }
  };
}

checkPltDaddyData();
