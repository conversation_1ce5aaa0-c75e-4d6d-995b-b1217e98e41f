import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "postgresql://postgres:<EMAIL>:5432/americans_united_inc"
    }
  }
});

async function checkAllShops() {
  try {
    console.log('=== CHECKING ALL SHOPS IN DATABASE ===');

    // Get all unique shops
    const allShops = await prisma.shippingCost.findMany({
      select: { shop: true },
      distinct: ['shop']
    });

    console.log(`Found ${allShops.length} unique shops:`);
    allShops.forEach((shop, index) => {
      console.log(`${index + 1}. ${shop.shop}`);
    });

    // Now check each shop's data
    for (const shopRecord of allShops) {
      const shop = shopRecord.shop;
      console.log(`\n=== DATA FOR SHOP: ${shop} ===`);

      const records = await prisma.shippingCost.findMany({
        where: { shop },
        orderBy: [
          { year: 'desc' },
          { month: 'desc' },
          { storeId: 'asc' }
        ]
      });

      console.log(`Records: ${records.length}`);

      if (records.length > 0) {
        // Group by month/year
        const monthNames = [
          'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];

        const groupedData = {};
        let totalAmount = 0;

        records.forEach(record => {
          const monthKey = `${record.year}-${record.month}`;
          const monthName = `${monthNames[record.month]} ${record.year}`;

          if (!groupedData[monthKey]) {
            groupedData[monthKey] = {
              monthName,
              records: [],
              totalAmount: 0
            };
          }

          groupedData[monthKey].records.push(record);
          groupedData[monthKey].totalAmount += Number(record.totalAmount);
          totalAmount += Number(record.totalAmount);
        });

        Object.keys(groupedData).sort().forEach(monthKey => {
          const data = groupedData[monthKey];
          console.log(`  ${data.monthName}: ${data.records.length} records, $${data.totalAmount.toFixed(2)}`);
        });

        console.log(`  TOTAL: $${totalAmount.toFixed(2)}`);
      }
    }

  } catch (error) {
    console.error('Error checking shops:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAllShops();
