import csv

total_carrier_fee = 0
total_shipments = 0
voided_carrier_fee = 0
voided_shipments = 0
valid_carrier_fee = 0
valid_shipments = 0
draft_order_carrier_fee = 0
draft_order_shipments = 0
valid_non_draft_carrier_fee = 0
valid_non_draft_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/atp-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row['Carrier - Fee'])
        void_flag = row['Shipment - Void Flag']
        order_source = row['Order - Source']
        
        total_carrier_fee += carrier_fee
        total_shipments += 1
        
        # Check if shipment is voided
        if void_flag.lower() == 'true':
            voided_carrier_fee += carrier_fee
            voided_shipments += 1
        else:
            valid_carrier_fee += carrier_fee
            valid_shipments += 1
            
            # Check if it's a draft order among valid shipments
            if order_source == 'shopify_draft_order':
                draft_order_carrier_fee += carrier_fee
                draft_order_shipments += 1
            else:
                valid_non_draft_carrier_fee += carrier_fee
                valid_non_draft_shipments += 1

print("=== ANALYSIS OF VOIDED SHIPMENTS ===")
print(f"Total shipments in CSV: {total_shipments}")
print(f"Total carrier fees in CSV: ${total_carrier_fee:.2f}")
print()
print(f"Voided shipments: {voided_shipments}")
print(f"Voided carrier fees: ${voided_carrier_fee:.2f}")
print()
print(f"Valid (non-voided) shipments: {valid_shipments}")
print(f"Valid carrier fees: ${valid_carrier_fee:.2f}")
print()
print(f"Valid draft order shipments: {draft_order_shipments}")
print(f"Valid draft order carrier fees: ${draft_order_carrier_fee:.2f}")
print()
print(f"Valid non-draft shipments: {valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${valid_non_draft_carrier_fee:.2f}")
print()

print("=== EXPECTED TOTALS (EXCLUDING VOIDED) ===")
print(f"Valid carrier fees: ${valid_carrier_fee:.2f}")
markup = valid_carrier_fee * 0.10
total_with_markup = valid_carrier_fee + markup
fulfillment_costs = valid_shipments * 1.50
expected_total = total_with_markup + fulfillment_costs

print(f"10% markup: ${markup:.2f}")
print(f"Total with markup: ${total_with_markup:.2f}")
print(f"Fulfillment costs ({valid_shipments} x $1.50): ${fulfillment_costs:.2f}")
print(f"Expected total: ${expected_total:.2f}")
print()

print("=== EXPECTED TOTALS (EXCLUDING VOIDED AND DRAFT ORDERS) ===")
print(f"Valid non-draft carrier fees: ${valid_non_draft_carrier_fee:.2f}")
markup_non_draft = valid_non_draft_carrier_fee * 0.10
total_with_markup_non_draft = valid_non_draft_carrier_fee + markup_non_draft
fulfillment_costs_non_draft = valid_non_draft_shipments * 1.50
expected_total_non_draft = total_with_markup_non_draft + fulfillment_costs_non_draft

print(f"10% markup: ${markup_non_draft:.2f}")
print(f"Total with markup: ${total_with_markup_non_draft:.2f}")
print(f"Fulfillment costs ({valid_non_draft_shipments} x $1.50): ${fulfillment_costs_non_draft:.2f}")
print(f"Expected total: ${expected_total_non_draft:.2f}")
print()

print("=== COMPARISON WITH REPORTED VALUES ===")
print("You reported seeing: 1332 items for a total of $9,192.89 (after markup)")
reported_after_markup = 9192.89
reported_items = 1332

# Work backwards to find the base shipping cost
reported_base_shipping = reported_after_markup / 1.10
print(f"Implied base shipping cost: ${reported_base_shipping:.2f}")
print(f"Difference from valid non-draft: ${reported_base_shipping - valid_non_draft_carrier_fee:.2f}")
print(f"Item count difference: {reported_items - valid_non_draft_shipments}")
