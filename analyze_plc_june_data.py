import csv

total_carrier_fee = 0
total_shipments = 0
voided_carrier_fee = 0
voided_shipments = 0
valid_carrier_fee = 0
valid_shipments = 0
draft_order_carrier_fee = 0
draft_order_shipments = 0
valid_non_draft_carrier_fee = 0
valid_non_draft_shipments = 0

with open(
    "/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/plc-shipments-june.csv",
    "r",
) as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row["Carrier Fee"])
        # June CSV doesn't have void flag or order source columns, assume all are valid non-draft
        void_flag = "false"
        order_source = "regular"

        total_carrier_fee += carrier_fee
        total_shipments += 1

        # Check if shipment is voided
        if void_flag.lower() == "true":
            voided_carrier_fee += carrier_fee
            voided_shipments += 1
        else:
            valid_carrier_fee += carrier_fee
            valid_shipments += 1

            # Check if it's a draft order among valid shipments
            if order_source == "shopify_draft_order":
                draft_order_carrier_fee += carrier_fee
                draft_order_shipments += 1
            else:
                valid_non_draft_carrier_fee += carrier_fee
                valid_non_draft_shipments += 1

print("=== PHASELINECO-FULFILLMENT JUNE CSV ANALYSIS ===")
print(f"Total shipments in CSV: {total_shipments}")
print(f"Total carrier fees in CSV: ${total_carrier_fee:.2f}")
print()
print(f"Voided shipments: {voided_shipments}")
print(f"Voided carrier fees: ${voided_carrier_fee:.2f}")
print()
print(f"Valid (non-voided) shipments: {valid_shipments}")
print(f"Valid carrier fees: ${valid_carrier_fee:.2f}")
print()
print(f"Valid draft order shipments: {draft_order_shipments}")
print(f"Valid draft order carrier fees: ${draft_order_carrier_fee:.2f}")
print()
print(f"Valid non-draft shipments: {valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${valid_non_draft_carrier_fee:.2f}")
print()

print("=== EXPECTED TOTALS (EXCLUDING VOIDED AND DRAFT ORDERS) ===")
print(f"Expected shipments to process: {valid_non_draft_shipments}")
print(f"Expected base shipping cost: ${valid_non_draft_carrier_fee:.2f}")
markup = valid_non_draft_carrier_fee * 0.10
total_with_markup = valid_non_draft_carrier_fee + markup
fulfillment_costs = valid_non_draft_shipments * 1.50
expected_total = total_with_markup + fulfillment_costs

print(f"Expected 10% markup: ${markup:.2f}")
print(f"Expected total with markup: ${total_with_markup:.2f}")
print(
    f"Expected fulfillment costs ({valid_non_draft_shipments} x $1.50): ${fulfillment_costs:.2f}"
)
print(f"Expected grand total: ${expected_total:.2f}")

print()
print("=== COMPARISON WITH DATABASE JUNE RESULTS ===")
print("Database showed: 2357 items, $16,122.25 base, $17,734.47 total")
db_base = 16122.25
db_total = 17734.47
db_items = 2357

print("CSV vs DB differences:")
print(
    f"  Quantity: {db_items - valid_non_draft_shipments} ({db_items} DB vs {valid_non_draft_shipments} CSV)"
)
print(
    f"  Base cost: ${db_base - valid_non_draft_carrier_fee:.2f} (${db_base:.2f} DB vs ${valid_non_draft_carrier_fee:.2f} CSV)"
)
print(
    f"  Total cost: ${db_total - total_with_markup:.2f} (${db_total:.2f} DB vs ${total_with_markup:.2f} CSV)"
)
